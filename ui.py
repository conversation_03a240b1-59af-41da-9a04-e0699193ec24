import socket
import sys
import subprocess
import signal
import os
from pathlib import Path

import psutil
import time
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout,
    QHBoxLayout, QLabel, QLineEdit, QPushButton, QTextEdit,
    QGroupBox, QMessageBox, QFrame, QProgressBar
)
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QTimer, pyqtSlot
from PyQt5.QtGui import QFont, QColor, QPalette, QLinearGradient, QBrush, QIcon

# 深色主题颜色定义
DARK_BG = QColor(30, 30, 40)
DARK_CARD = QColor(45, 45, 60)
ACCENT_COLOR = QColor(65, 105, 225)  # 皇家蓝
SUCCESS_COLOR = QColor(46, 204, 113)  # 翠绿
ERROR_COLOR = QColor(231, 76, 60)  # 宝石红
WARNING_COLOR = QColor(241, 196, 15)  # 向日葵黄
TEXT_LIGHT = QColor(220, 220, 230)
TEXT_MUTED = QColor(170, 170, 190)

class OutputReader(QThread):
    output_received = pyqtSignal(str)  # 添加信号声明

    def __init__(self, process):
        super().__init__()
        self.process = process

    def run(self):
        while self.process.poll() is None:
            try:
                line = self.process.stdout.readline().decode().strip()
                if line:
                    self.output_received.emit(line)  # 发射信号
            except Exception as e:
                print(f"日志读取错误: {str(e)}")

class CustomButton(QPushButton):
    """带有悬停动画效果的按钮"""

    def __init__(self, text, bg_color, hover_color, parent=None):
        super().__init__(text, parent)
        self.default_bg_color = bg_color
        self.hover_bg_color = hover_color

        # 初始样式
        self.update_style(self.default_bg_color)

    def enterEvent(self, event):
        self.update_style(self.hover_bg_color)

    def leaveEvent(self, event):
        self.update_style(self.default_bg_color)

    def update_style(self, color):
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {color.name()};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {self.hover_bg_color.name()};
            }}
            QPushButton:disabled {{
                background-color: #555;
                color: #999;
            }}
        """)


class FlaskProcessManager:
    """管理 Flask 进程的服务管理器"""

    def __init__(self):
        self.process = None
        self.port = None
        self.running = False

    def start_service(self, port):
        """启动 Flask 服务"""
        if self.running:
            return "服务已在运行中"

        try:
            env = os.environ.copy()
            env["SERVER_PORT"] = str(port)
            self.port = port

            # 创建标志位
            if os.name == 'nt':  # Windows
                creation_flags = subprocess.CREATE_NEW_PROCESS_GROUP
                self.process = subprocess.Popen(
                    ["python", "api.py"],
                    env=env,
                    creationflags=creation_flags,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT
                )
            else:  # macOS/Linux
                # 使用进程组以便可以终止整个进程树
                self.process = subprocess.Popen(
                    ["python", "api.py"],
                    env=env,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    start_new_session=True  # 创建新的进程组
                )

            self.running = True
            return f"Flask 服务已在端口 {port} 启动 (PID: {self.process.pid})，IP：{get_ip()}"
        except Exception as e:
            return f"启动服务时出错: {str(e)}"

    def stop_service(self):
        """停止 Flask 服务"""
        if not self.running or not self.process:
            return "没有运行中的服务"

        try:
            # 尝试优雅地停止服务
            try:
                if os.name == 'nt':  # Windows
                    self.process.send_signal(signal.CTRL_BREAK_EVENT)
                else:  # macOS/Linux
                    # 发送信号到整个进程组
                    os.killpg(os.getpgid(self.process.pid), signal.SIGTERM)

                # 等待一段时间让进程退出
                self.process.wait(timeout=5)
            except (subprocess.TimeoutExpired, ProcessLookupError):
                # 如果超时，强制终止进程
                self.force_stop_process()
            except Exception as e:
                return f"停止服务时出错: {str(e)}"
            finally:
                # 确保状态被重置
                self.running = False
                self.process = None
                self.port = None
                return "Flask 服务已停止"
        except Exception as e:
            return f"停止服务时出错: {str(e)}"

    def force_stop_process(self):
        """强制终止进程"""
        if self.process is None:
            return
        if os.name == 'nt':  # Windows
            self.process.terminate()
        else:  # macOS/Linux
            try:
                # 终止整个进程树
                parent = psutil.Process(self.process.pid)
                children = parent.children(recursive=True)
                for child in children:
                    child.terminate()
                parent.terminate()
            except psutil.NoSuchProcess:
                # 如果进程已经不存在
                pass
            except Exception as e:
                # 如果终止失败，尝试杀死
                try:
                    parent.kill()
                except:
                    pass
        # 重置状态
        self.running = False
        self.process = None
        self.port = None


# 后台任务工作线程
class ServiceThread(QThread):
    finished = pyqtSignal(str, bool)  # (消息, 是否成功)

    def __init__(self, manager, action, port=None):
        super().__init__()
        self.manager = manager
        self.action = action  # 'start' 或 'stop'
        self.port = port

    def run(self):
        try:
            if self.action == 'start':
                result = self.manager.start_service(self.port)
                success = self.manager.running
            elif self.action == 'stop':
                result = self.manager.stop_service()
                success = not self.manager.running
            else:
                result = "未知操作"
                success = False

            self.finished.emit(result, success)
        except Exception as e:
            self.finished.emit(f"操作失败: {str(e)}", False)


# 主应用窗口
class FlaskControllerApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.manager = FlaskProcessManager()
        self.setup_styles()
        self.init_ui()
        self.setWindowTitle("AICodeReview服务")
        self.setGeometry(100, 100, 700, 600)
        self.setMinimumSize(600, 500)

    def setup_styles(self):
        """设置全局深色主题样式"""
        palette = QPalette()
        palette.setColor(QPalette.Window, DARK_BG)
        palette.setColor(QPalette.WindowText, TEXT_LIGHT)
        palette.setColor(QPalette.Base, QColor(35, 35, 45))
        palette.setColor(QPalette.AlternateBase, DARK_CARD)
        palette.setColor(QPalette.ToolTipBase, DARK_CARD)
        palette.setColor(QPalette.ToolTipText, TEXT_LIGHT)
        palette.setColor(QPalette.Text, TEXT_LIGHT)
        palette.setColor(QPalette.Button, ACCENT_COLOR)
        palette.setColor(QPalette.ButtonText, TEXT_LIGHT)
        palette.setColor(QPalette.BrightText, ERROR_COLOR)
        palette.setColor(QPalette.Link, ACCENT_COLOR)
        palette.setColor(QPalette.Highlight, ACCENT_COLOR)
        palette.setColor(QPalette.HighlightedText, TEXT_LIGHT)
        QApplication.setPalette(palette)
        QApplication.setStyle("Fusion")

        # 设置全局样式
        self.setStyleSheet(f"""
            QWidget {{
                font-family: 'Segoe UI', 'Arial';
                background-color: {DARK_BG.name()};
            }}
            QGroupBox {{
                border: 1px solid rgba(90, 90, 120, 100);
                border-radius: 10px;
                margin-top: 20px;
                padding-top: 15px;
                padding-bottom: 15px;
                padding-left: 15px;
                padding-right: 15px;
                font-size: 14px;
                font-weight: bold;
                color: #AAA;
            }}
            QLineEdit {{
                background-color: rgba(40, 40, 50, 180);
                color: {TEXT_LIGHT.name()};
                border: 1px solid rgba(90, 90, 120, 100);
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }}
            QLabel {{
                color: {TEXT_LIGHT.name()};
                font-size: 14px;
            }}
            QProgressBar {{
                border: 1px solid rgba(90, 90, 120, 100);
                border-radius: 5px;
                background: rgba(40, 40, 50, 180);
                text-align: center;
                font-size: 12px;
                height: 20px;
            }}
            QProgressBar::chunk {{
                background-color: #4CAF50;
                border-radius: 4px;
            }}
        """)

    def init_ui(self):
        # 主部件
        main_widget = QWidget()
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 顶部标题和Logo
        title_layout = QHBoxLayout()
        title_icon = QLabel()
        title_icon.setPixmap(QIcon.fromTheme("system-services").pixmap(40, 40))
        title_layout.addWidget(title_icon)

        title = QLabel("AICodeReview服务")
        title_font = QFont("Arial", 24, QFont.Bold)
        title.setFont(title_font)

        # 创建渐变色效果
        gradient = QLinearGradient(0, 0, 300, 0)
        gradient.setColorAt(0, ACCENT_COLOR)
        gradient.setColorAt(1, SUCCESS_COLOR)
        palette = title.palette()
        palette.setBrush(QPalette.WindowText, QBrush(gradient))
        title.setPalette(palette)
        title_layout.addWidget(title)
        title_layout.addStretch()

        main_layout.addLayout(title_layout)

        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("background-color: rgba(90, 90, 120, 80); height: 1px;")
        main_layout.addWidget(separator)

        # 设置组
        settings_box = QGroupBox("服务设置")
        settings_layout = QVBoxLayout()

        # 端口输入
        port_layout = QHBoxLayout()
        port_layout.addWidget(QLabel("监听端口:"))
        self.port_input = QLineEdit("5001")
        self.port_input.setFixedWidth(100)
        port_layout.addWidget(self.port_input)
        port_layout.addStretch()

        settings_layout.addLayout(port_layout)
        settings_box.setLayout(settings_layout)
        main_layout.addWidget(settings_box)

        # 控制按钮
        control_layout = QHBoxLayout()

        self.start_button = CustomButton("▶ 启动服务", ACCENT_COLOR, SUCCESS_COLOR)
        self.start_button.clicked.connect(self.start_service)
        self.start_button.setFixedHeight(45)
        self.start_button.setFont(QFont("Arial", 12))

        self.stop_button = CustomButton("■ 停止服务", ERROR_COLOR, WARNING_COLOR)
        self.stop_button.clicked.connect(self.stop_service)
        self.stop_button.setFont(QFont("Arial", 12))
        self.stop_button.setFixedHeight(45)
        self.stop_button.setEnabled(False)

        # 状态指示器
        self.status_indicator = QLabel("⚪")
        self.status_indicator.setFont(QFont("Arial", 24))
        self.status_indicator.setAlignment(Qt.AlignCenter)
        self.status_indicator.setFixedWidth(60)
        self.status_indicator.setStyleSheet(f"color: {TEXT_MUTED.name()};")

        control_layout.addWidget(self.start_button)
        control_layout.addWidget(self.status_indicator)
        control_layout.addWidget(self.stop_button)

        main_layout.addLayout(control_layout)

        # 状态信息
        status_layout = QHBoxLayout()

        # 状态信息卡片
        status_card = QFrame()
        status_card.setStyleSheet(f"""
            background-color: {DARK_CARD.name()};
            border-radius: 10px;
            padding: 15px;
        """)
        status_card_layout = QHBoxLayout()

        self.status_label = QLabel("服务状态: <b>已停止</b>")
        self.status_label.setFont(QFont("Arial", 12))
        self.status_label.setStyleSheet(f"color: {TEXT_LIGHT.name()};")

        # 状态元数据
        meta_layout = QHBoxLayout()
        self.pid_label = QLabel("进程ID: -")
        self.pid_label.setStyleSheet(f"color: {TEXT_MUTED.name()}; font-size: 13px;")
        self.port_label = QLabel("端口: -")
        self.port_label.setStyleSheet(f"color: {TEXT_MUTED.name()}; font-size: 13px;")
        meta_layout.addWidget(self.pid_label)
        meta_layout.addStretch()
        meta_layout.addWidget(self.port_label)

        status_card_layout.addWidget(self.status_label)
        status_card_layout.addLayout(meta_layout)
        status_card.setLayout(status_card_layout)

        status_layout.addWidget(status_card)
        main_layout.addLayout(status_layout)

        # 日志区域
        log_box = QGroupBox("操作日志")
        log_layout = QVBoxLayout()

        self.log_output = QTextEdit()
        self.log_output.setReadOnly(True)
        self.log_output.setFrameStyle(QFrame.NoFrame)
        self.log_output.setStyleSheet(f"""
            QTextEdit {{
                background-color: rgba(25, 25, 35, 200);
                color: {TEXT_LIGHT.name()};
                border: none;
                border-radius: 8px;
                padding: 10px;
                font-family: 'Consolas', 'Courier New';
                font-size: 13px;
            }}
        """)

        log_layout.addWidget(self.log_output)
        log_box.setLayout(log_layout)
        main_layout.addWidget(log_box)

        # 强制停止按钮
        force_stop_layout = QHBoxLayout()
        force_stop_layout.addStretch()

        self.force_stop_btn = CustomButton("强制停止服务", WARNING_COLOR, ERROR_COLOR)
        self.force_stop_btn.setFont(QFont("Arial", 11))
        self.force_stop_btn.setFixedHeight(40)
        self.force_stop_btn.setEnabled(False)
        self.force_stop_btn.clicked.connect(self.force_stop_service)

        force_stop_layout.addWidget(self.force_stop_btn)
        main_layout.addLayout(force_stop_layout)

        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)

        # 动画控制变量
        self.status_animation_timer = QTimer()
        self.status_animation_timer.timeout.connect(self.update_status_indicator)
        self.status_animation_timer.start(500)  # 每0.5秒更新一次

    def log_message(self, message, level="info"):
        """在日志区域添加彩色消息"""
        color_map = {
            "error": ERROR_COLOR.name(),
            "warning": WARNING_COLOR.name(),
            "success": SUCCESS_COLOR.name(),
            "info": TEXT_MUTED.name()
        }

        color = color_map.get(level, TEXT_MUTED.name())
        formatted = f'<span style="color:{color};">{message}</span>'
        self.log_output.append(formatted)

    def update_status_indicator(self):
        """更新状态指示器的动画效果"""
        if self.manager.running:
            text = "●" if int(time.time() * 2) % 2 == 0 else "◐"
            color = SUCCESS_COLOR.name()
        else:
            text = "⚪"
            color = TEXT_MUTED.name()

        # 直接设置样式而不是通过动画系统
        self.status_indicator.setText(text)
        self.status_indicator.setStyleSheet(f"color: {color};")

    def update_status_text(self):
        """更新状态文本"""
        if self.manager.running and self.manager.process:
            status_text = f"<span style='color:{SUCCESS_COLOR.name()}'>● 服务运行中</span>"
            self.status_label.setText(f"服务状态: {status_text}")
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.force_stop_btn.setEnabled(True)
            self.pid_label.setText(f"进程ID: <b style='color:{TEXT_LIGHT.name()}'>{self.manager.process.pid}</b>")
            self.port_label.setText(f"端口: <b style='color:{TEXT_LIGHT.name()}'>{self.manager.port}</b>")
        else:
            status_text = f"<span style='color:{ERROR_COLOR.name()}'>◌ 服务已停止</span>"
            self.status_label.setText(f"服务状态: {status_text}")
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.force_stop_btn.setEnabled(False)
            self.pid_label.setText(f"进程ID: <span style='color:{TEXT_MUTED.name()}'>-</span>")
            self.port_label.setText(f"端口: <span style='color:{TEXT_MUTED.name()}'>-</span>")

    def start_service(self):
        """启动服务按钮处理"""
        try:
            port = int(self.port_input.text().strip())
            if port < 1 or port > 65535:
                raise ValueError()

            self.log_message(f"正在启动服务，端口: {port}...", "info")

            # 禁用按钮防止重复点击
            self.start_button.setEnabled(False)

            # 创建并启动工作线程
            self.service_thread = ServiceThread(self.manager, 'start', port)
            self.service_thread.finished.connect(self.handle_service_result)
            self.service_thread.start()

        except ValueError:
            self.log_message("错误：端口号必须是1-65535之间的数字", "error")
            QMessageBox.critical(self, "无效端口", "请输入有效的端口号（1-65535）")
            self.start_button.setEnabled(True)

    def stop_service(self):
        """停止服务按钮处理"""
        self.log_message("正在停止服务...", "info")

        # 禁用按钮防止重复点击
        self.stop_button.setEnabled(False)
        self.force_stop_btn.setEnabled(False)

        # 创建并启动工作线程
        self.service_thread = ServiceThread(self.manager, 'stop')
        self.service_thread.finished.connect(self.handle_service_result)
        self.service_thread.start()

    def force_stop_service(self):
        """强制停止服务"""
        if self.manager.running:
            self.log_message("正在强制停止服务...", "warning")

            # 禁用按钮防止重复点击
            self.stop_button.setEnabled(False)
            self.force_stop_btn.setEnabled(False)

            try:
                self.manager.force_stop_process()
                self.log_message("服务已强制停止", "warning")
                self.update_status_text()
            except Exception as e:
                self.log_message(f"强制停止失败: {str(e)}", "error")
        else:
            self.log_message("没有运行中的服务可以强制停止", "warning")
            QMessageBox.information(self, "服务状态", "当前没有运行中的服务")

    def handle_service_result(self, message, success):
        """处理服务启动/停止结果"""
        if success:
            self.reader_thread = OutputReader(self.manager.process)
            self.reader_thread.output_received.connect(self.handle_log_output)
            self.reader_thread.start()
            self.log_message(message, "success")
        else:
            self.log_message(message, "error")

        self.update_status_text()
        self.service_thread.deleteLater()

    # 新增槽函数处理日志
    @pyqtSlot(str)
    def handle_log_output(self, msg):
        self.log_message(msg, "info")

    def closeEvent(self, event):
        """窗口关闭时停止服务"""
        if self.manager.running:
            reply = QMessageBox.question(
                self, '确认关闭',
                '服务仍在运行中，确定要退出吗？',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.log_message("程序退出中...停止服务", "info")
                if self.manager.running:
                    self.force_stop_service()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

def get_ip():
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        # 连接一个公共DNS（不需要真实通信）
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
    except Exception:
        ip = "127.0.0.1"  # 失败时回退到本地地址
    finally:
        s.close()
    return ip

# 主应用
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = FlaskControllerApp()

    # 获取项目根目录
    project_root = Path(__file__).parent
    
    # 设置窗口图标
    try:
        app.setWindowIcon(QIcon(f'{project_root}/icon/icon.png'))
        window.setWindowIcon(QIcon(f'{project_root}/icon/icon.png'))
    except Exception as e:
        print(f"加载图标失败: {str(e)}")

    window.show()
    sys.exit(app.exec_())