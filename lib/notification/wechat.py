import json

import requests
from datetime import datetime

from constants import SERVER_URL
from log import get_logger
from lib.reviewer_config import reviewer_config
from lib.user_mapping import user_mapping

# 默认webhook_url（当没有找到组配置时使用）
DEFAULT_WEBHOOK_URL = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=77ed84e2-8d4f-484d-b89e-fcd83c410474'


# webhook_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=77ed84e2-8d4f-484d-b89e-fcd83c410474' #代码对比api
# webhook_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c4754c3a-9ccd-4e9e-89fd-b945b76f54bf' # 代码提交api

def send_wechat_notification(user_name, commit_url, commit_title, project_name, project_url, branch, logger, reviewers, is_recheck=False):
    # 根据项目名获取对应的webhook_url
    webhook_url = reviewer_config.get_webhook_url_by_repository(project_name) or DEFAULT_WEBHOOK_URL
    logger.info(f"使用webhook_url: {webhook_url}")
    
    msg_data = build_markdown_message(user_name, commit_url, commit_title, project_name, project_url, branch, is_recheck, reviewers)
    notification_msg_data = build_text_message(commit_title, reviewers)
    logger.info(f"微信发送内容：{msg_data}")
    send_request(webhook_url, msg_data, logger)
    if notification_msg_data:
        send_request(webhook_url, notification_msg_data, logger)

def build_markdown_message(user_name, commit_url, commit_title, project_name, project_url, branch, is_recheck, reviewers):
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    recheck_prompt = "\n><font color=\"warning\">提示：当前提交还未审核，请及时进行确认</font>" if is_recheck else ""
    return {
        "msgtype": "markdown",
        "markdown": {
            "content": f'{project_name} 代码审核' +
                       f'\n>构建项目：[{project_name}]({project_url})' +
                       f'\n>构建版本：{branch}\n>审核时间：{current_time}' +
                       f'\n>AI代码审核结果链接：[{commit_title}]({commit_url})' +
                       f'\n>构建人：@{user_name}' +
                       f'\n>审核人：{format_reviewers(reviewers)}' +
                       recheck_prompt
        }
    }

def build_text_message(commit_title, reviewers):
    user_ids = []
    for reviewer in reviewers:
        user_id = user_mapping.get_uid_by_username(reviewer)
        if user_id:
            user_ids.append(user_id)
    if not user_ids:
        return None
    return {
        "msgtype": "text",
        "text": {
            "content": f'当前提交：{commit_title}，AI代码审核结果已更新!',
            "mentioned_list": user_ids
        }
    }

def format_reviewers(reviewers):
    if not reviewers:  # 处理空列表情况
        return ""
    # 为每个标签添加@前缀并用逗号分隔
    return "，".join(f"@{tag}" for tag in reviewers)

def send_request(url, data, logger):
    """ 发送请求并返回 JSON 响应 """
    try:
        response = requests.post(url, json=data, headers={'Content-Type': 'application/json'})
        response.raise_for_status()  # 触发 HTTP 错误
        return response.json()
    except requests.RequestException as e:
        logger.error(f"企业微信消息发送请求失败! url:{url}, error: {e}")
    except json.JSONDecodeError as e:
        logger.error(f"企业微信返回的 JSON 解析失败! url:{url}, error: {e}")
    return None


def send_review_feedback_notification(record, logger):
    """发送审核反馈完成通知"""
    
    # 处理记录格式（可能是对象或字典）
    if isinstance(record, dict):
        # 如果是字典格式，直接使用
        reviewers = record.get('reviewers', [])
        record_obj = type('Record', (), record)()  # 创建模拟对象
    else:
        # 如果是对象格式，调用方法
        reviewers = record.get_reviewers_list()
        record_obj = record
    
    if not reviewers:
        logger.info("没有审核人员，跳过通知")
        return
    
    # 根据项目名获取对应的webhook_url
    project_name = getattr(record_obj, 'project_name', None)
    webhook_url = reviewer_config.get_webhook_url_by_repository(project_name) or DEFAULT_WEBHOOK_URL
    logger.info(f"使用webhook_url: {webhook_url}")
    
    msg_data = build_feedback_notification_message(record_obj, reviewers)
    logger.info(f"审核反馈通知内容：{msg_data}")
    send_request(webhook_url, msg_data, logger)


def build_feedback_notification_message(record, reviewers):
    """构造审核反馈通知消息"""
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    return {
        "msgtype": "markdown",
        "markdown": {
            "content": f'✅ 代码审核反馈已完成' +
                       f'\n>项目：[{record.project_name or "未知项目"}]({record.gitlab_project_url or ""})' +
                       f'\n>分支：{record.branch_name or "未知分支"}' +
                       f'\n>提交：[{record.commit_title or "代码提交"}]({record.commit_url})' +
                       f'\n>提交人：@{record.user_name or "未知用户"}' +
                       f'\n>审核时间：{current_time}' +
                       f'\n>审核人员：{format_reviewers(reviewers)}'
        }
    }

def send_group_pending_notification(group_key, group_info, pending_records, logger):
    """为组发送待审核汇总通知"""
    try:
        group_name = group_info.get('name', group_key)
        webhook_url = group_info.get('webhook_url')
        
        if not webhook_url:
            logger.warning(f"组 '{group_key}' 没有配置webhook_url，跳过通知")
            return
        
        # 构建汇总消息
        message = build_group_pending_message(group_key, group_name, pending_records)
        
        # 发送通知
        send_request(webhook_url, message, logger)
        
        logger.info(f"已发送组 '{group_key}' 待审核汇总通知")
        
    except Exception as e:
        logger.error(f"发送组 '{group_key}' 通知失败: {str(e)}")

def build_group_pending_message(group_key, group_name, pending_records):
    """构建组待审核汇总消息"""
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 按项目分组统计
    project_stats = {}
    for record in pending_records:
        project_name = record.project_name or "未知项目"
        if project_name not in project_stats:
            project_stats[project_name] = []
        project_stats[project_name].append(record)
    
    # 构建简化的消息内容
    content = f"🔔 {group_name} - 待审核代码提醒\n"
    content += f">检查时间：{current_time}\n"
    content += f">待审核项目数：{len(project_stats)}\n"
    content += f">待审核记录数：{len(pending_records)}\n"
    content += f'>审核面板：[审核面板]({SERVER_URL}/media/review_dashboard.html?group={group_key})\n'
    content += "<font color=\"warning\">请及时审核相关代码提交！</font>"
    
    return {
        "msgtype": "markdown",
        "markdown": {
            "content": content
        }
    }

if __name__ == '__main__':
    # 测试企微消息
    # msg_data = build_text_message("邹龙生", "test", "test-测试")
    markdown_msg = build_markdown_message("邹龙生",
                                      "",
                                      "m-增加自营登录方式",
                                      "TestAICodeReview-JAVA",
                                      "https://code.37ops.com/zoulongsheng/testaicodereview-java",
                                      "refs/heads/master", False, ['邹龙生', '萧国铿'])
    text_msg = build_text_message('m-增加自营登录方式', ['邹龙生', '萧国铿'])
    send_request(DEFAULT_WEBHOOK_URL, markdown_msg, get_logger('wechat_test'))
    send_request(DEFAULT_WEBHOOK_URL, text_msg, get_logger('wechat_test'))
