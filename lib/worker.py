import shutil
import traceback
from lib.history.history_handler import compare_with_history_code, clone_code
from lib.llm.code_reviewer import CodeR<PERSON>iewer
from lib.notification.wechat import send_wechat_notification
from lib.webhook_handler import PushHandler, filter_changes
from log import remove_logger
from lib.model.ApiUsage import update_issue_count, add_review_record
from lib.reviewer_config import get_reviewers_for_commit


def should_skip_code_review(branch_name: str, commit_title: str) -> bool:
    """
    检查是否需要跳过代码审核
    
    Args:
        branch_name: 分支名
        commit_title: 提交标题
        
    Returns:
        如果应该跳过代码审核返回True，否则返回False
    """
    if not branch_name or not commit_title:
        return False
    
    # 转换为小写进行匹配
    branch_lower = branch_name.lower()
    title_lower = commit_title.lower()
    
    # 检查分支名是否包含test或测试
    if 'test' in branch_lower or '测试' in branch_lower:
        return True
    
    # 检查提交标题是否包含test或测试
    if 'test' in title_lower or '测试' in title_lower:
        return True
    
    return False


def handle_push_event(webhook_data: dict, gitlab_token: str, gitlab_url: str, gitlab_url_slug: str, project_root_path: str, endpoint: str):
    handler = PushHandler(webhook_data, gitlab_token, gitlab_url)
    logger = handler.logger
    try:
        logger.info('Push Hook event received')
        commits = handler.get_push_commits()
        if not commits:
            logger.info('Failed to get commits')
            # 清理log
            clean_log_file(handler, project_root_path)
            return

        review_result = None
        # 获取PUSH的changes
        changes = handler.get_push_changes()
        logger.info('changes: %s', changes)
        changes = filter_changes(changes)
        logger.info('filter_changes: %s', changes)

        if not changes:
            logger.info('未检测到PUSH代码的修改,修改文件可能不满足SUPPORTED_EXTENSIONS。')
            # 清理log
            clean_log_file(handler, project_root_path)
            return
        review_result = "关注的文件没有修改"

        # 获取审核人员
        reviewers = get_reviewers_for_commit(handler.project_name, handler.branch_name)
        handler.logger.info(f"获取到审核人员: {reviewers}")
        
        # 检查是否需要跳过代码审核（测试分支或测试提交）
        if should_skip_code_review(handler.branch_name, handler.last_commit_title):
            handler.logger.info(f"跳过代码审核 - 分支名: {handler.branch_name}, 提交标题: {handler.last_commit_title}")
            # 仍然记录到数据库，但标记为测试代码
            add_review_record(handler.last_commit_url, 0, handler.user_name, handler.last_commit_title,
                              handler.gitlab_project_url, handler.project_name, handler.branch_name, reviewers, useful=2)
            clean_log_file(handler, project_root_path)
            return
            
        if len(changes) > 0:
            # 代码规范，安全相关codereview
            safe_code_review(handler, changes, commits, endpoint, reviewers)
            # 相似代码codereview
            # similar_code_review(handler, changes, commits, project_root_path)
            # 推送企微通知
            send_wechat_notification(handler.user_name,
                                     handler.last_commit_url,
                                     handler.last_commit_title,
                                     handler.project_name,
                                     handler.gitlab_project_url,
                                     handler.branch_name,
                                     handler.logger,
                                     reviewers,
                                     False)
            # 上传日志文件&清理log
            handle_log_file(handler, project_root_path)
        else:
            update_issue_count(endpoint, 0)
            add_review_record(handler.last_commit_url, 0, handler.user_name, handler.last_commit_title,
                              handler.gitlab_project_url, handler.project_name, handler.branch_name, reviewers, useful=None)
            logger.info(f'[handle_push_event] {review_result}')
            # 清理log
            clean_log_file(handler, project_root_path)

    except Exception as e:
        error_message = f'服务出现未知错误: {str(e)}\n{traceback.format_exc()}'
        logger.info('出现未知错误: %s', error_message)
        handle_log_file(handler, project_root_path)

def safe_code_review(handler, changes, commits, endpoint, reviewers):
    commits_text = ';'.join(commit.get('message', '').strip() for commit in commits)
    review_result = CodeReviewer(handler.logger).review_and_strip_code(str(changes), commits_text)
    issue_count = CodeReviewer.parse_issue_count(review_result)
    
    print(f'endpoint = {endpoint}, issue_count = {issue_count}')
    update_issue_count(endpoint, issue_count)
    add_review_record(handler.last_commit_url, issue_count, handler.user_name, handler.last_commit_title,
                      handler.gitlab_project_url, handler.project_name, handler.branch_name, reviewers, useful=None)
    # 将review结果提交到Gitlab的 notes
    handler.add_push_notes(f'# AI代码审核结果: \n{review_result}')

def similar_code_review(handler, changes, commits, project_root_path):
    commits_text = ';'.join(commit.get('message', '').strip() for commit in commits)
    target_clone_dir = f'{project_root_path}/history/code'
    logger = handler.logger
    logger.info(f'gitlab_project_url = {handler.gitlab_project_url}, target_clone_dir = {target_clone_dir}, branch_name = {handler.branch_name}')
    is_clone = clone_code(handler.gitlab_project_url, target_clone_dir, handler.branch_name, logger)
    if not is_clone:
        logger.error('仓库clone失败')
        return
    for change in changes:
        diff_code = change['diff']
        review_result, review_file_path = compare_with_history_code(target_clone_dir, diff_code, logger, commits_text)
        if review_result:
            logger.info(f'review_file_path = {review_file_path}, target_clone_dir = ${target_clone_dir}')
            review_file_path = str(review_file_path).replace(target_clone_dir, '')
            handler.add_push_notes(f'# AI相似代码文件{review_file_path}审核结果 \n {review_result}')
    # 删除仓库
    shutil.rmtree(target_clone_dir, ignore_errors=True)

def handle_log_file(handler, project_root_path):
    log_file_path = f'{project_root_path}/log/{handler.last_commit_id}.log'
    markdown_file = handler.upload_log_file(log_file_path)
    if markdown_file:
        handler.add_push_notes(f'AI审核处理日志文件：\n {markdown_file}')
    clean_log_file(handler, project_root_path)

def clean_log_file(handler, project_root_path):
    log_file_path = f'{project_root_path}/log/{handler.last_commit_id}.log'
    # 清除logger，删除日志文件
    remove_logger(handler.last_commit_id, log_file_path)
