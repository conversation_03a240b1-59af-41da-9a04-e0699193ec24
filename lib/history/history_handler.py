'''
历史相似代码比较
'''
import shutil
import traceback
from git import Repo, InvalidGitRepositoryError, GitCommandError
import os
import re
from pathlib import Path
from difflib import SequenceMatcher
import time

from lib.common_env import supported_extensions
from lib.llm.history_code_reviewer import HistoryCodeReviewer


def clone_code(repo_url, target_dir, branch, logger):
    try:
        if os.path.exists(target_dir):
            logger.info(f"目标目录 {target_dir} 已存在，正在删除...")
            shutil.rmtree(target_dir, ignore_errors=True)
        # clone和check到对应分支
        repo_name = repo_url.split('/')[-1].rstrip('.git')
        target_dir = os.path.join(target_dir, repo_name)
        robust_shallow_prev_clone(repo_url, target_dir, logger, branch)
        # 判断克隆仓库是否存在
        if os.path.exists(target_dir):
            return True
    except Exception as e:
        error_message = f'服务出现未知错误: {str(e)}\n{traceback.format_exc()}'
        logger.info('出现未知错误: %s', error_message)
    return False


def compare_with_history_code(target_dir, diff_code, logger, commit_text=""):
    try:
        # 匹配相似代码
        logger.info(f'change = {diff_code}')
        similar_code_dict = find_similar_code(target_dir, diff_code, logger)
        codes = similar_code_dict['codes']
        if not codes:
            return "", ""
        # AICodeReview
        history_code_reviewer = HistoryCodeReviewer(logger)
        review_result = history_code_reviewer.review_and_strip_code(diff_code, str(similar_code_dict['codes']), commit_text)
        review_file_path = similar_code_dict['file_path']
        return review_result, review_file_path
    except Exception as e:
        error_message = f'服务出现未知错误: {str(e)}\n{traceback.format_exc()}'
        logger.info('出现未知错误: %s', error_message)
    return "", ""


def robust_shallow_prev_clone(url, target_dir, logger, branch="master"):
    try:
        # 确保目标目录不存在或为空
        if os.path.exists(target_dir):
            if any(os.scandir(target_dir)):
                raise FileExistsError(f"目标目录 {target_dir} 非空")

        # 执行浅克隆
        repo = Repo.clone_from(
            url,
            target_dir,
            depth=2,
            single_branch=True,
            branch=branch
        )

        # 验证提交历史
        head = repo.head.commit
        if not head.parents:
            raise ValueError("最新提交是根提交，无前一提交")

        # 检出上一提交
        prev_commit = head.parents[0]
        repo.git.checkout(prev_commit, force=True)

        logger.info(f"成功检出前一提交: {prev_commit.hexsha[:7]}")
        return repo

    except GitCommandError as e:
        logger.info(f"Git命令错误: {e.stderr.strip()}")
    except InvalidGitRepositoryError:
        logger.info("无效的Git仓库URL")
    except ValueError as ve:
        logger.info(f"值错误: {str(ve)}")
    except Exception as e:
        logger.info(f"未知错误: {str(e)}")


def transform_diff_code(diff_content):
    """
        将diff格式内容转换为只包含新增行的代码块字符串

        参数:
            diff_content (str): diff格式的变更内容

        返回:
            str: 包含所有新增行的格式化的代码块字符串
        """
    # 分割为单独行进行处理
    lines = diff_content.split('\n')
    added_lines = []

    for line in lines:
        # 只处理以'+'开头的行（新增内容）
        if line.startswith('+'):
            # 跳过diff元信息行
            if line.startswith(('+++', '@@', '+ ')):
                continue

            # 移除开头的'+'符号，保留其余内容
            clean_line = line[1:]

            # 如果行不为空则保留
            if clean_line.strip():
                added_lines.append(clean_line)

    # 处理空结果的情况
    if not added_lines:
        return '"""\n"""'

    # 为每行添加4个空格的缩进
    indented_lines = ['    ' + line for line in added_lines]

    # 构建结果字符串
    result = '"""\n' + '\n'.join(indented_lines) + '\n"""'
    return result


def find_similar_code(repo_path, target_code, logger, similarity_threshold=0.2):
    """
    在代码库中查找相似的代码片段（跨文件比较并排序）

    参数：
        repo_path: Git仓库本地路径
        target_code: 要搜索的目标代码片段
        similarity_threshold: 相似度阈值 (0-1)
        top_n: 返回前N个最佳匹配
    """
    # 只处理新增代码
    diff_lines = target_code.split('\n')
    new_code_lines = []

    for line in diff_lines:
        # 仅处理以 '+' 开头的行（排除 diff 标记行 '+++'）
        if line.startswith('+') and not line.startswith('+++'):
            # 移除行首的 '+' 符号，保留原内容
            new_code_lines.append(line[1:])

    # 将处理后的行合并为新代码
    target_code = '\n'.join(new_code_lines)

    top_n = 1
    start_time = time.time()
    target_lines = [line.rstrip() for line in target_code.split('\n') if line.strip()]

    if not target_lines:
        logger.info("目标代码为空，请提供有效的代码片段")
        return

    logger.info(f"开始在仓库中搜索相似代码 (目标代码 {len(target_lines)} 行)...")

    # 存储所有匹配结果
    matches = []
    processed_files = 0
    total_files = sum(1 for _ in Path(repo_path).rglob('*.*'))

    # 简化代码的正则（保留数字、字母、下划线）
    pattern = re.compile(r'[^\w_]')

    for file_path in Path(repo_path).rglob('*.*'):
        processed_files += 1
        if processed_files % 100 == 0:
            elapsed = time.time() - start_time
            logger.info(f"已处理 {processed_files}/{total_files} 个文件 ({elapsed:.1f}s) 发现 {len(matches)} 个匹配\n")

        if not file_path.is_file() or file_path.suffix.lower() not in supported_extensions:
            continue

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                file_content = f.read()
                file_lines = [line.rstrip() for line in file_content.split('\n')]
                total_file_lines = len(file_lines)

                # 确保文件足够长
                if total_file_lines < len(target_lines):
                    continue

                # 将目标代码预处理为单一字符串（移除非字母数字字符，转为小写）
                clean_target = ''.join(pattern.sub('', line).lower() for line in target_lines)

                # 滑动窗口比较代码片段
                for i in range(0, total_file_lines - len(target_lines) + 1):
                    window = file_lines[i:i + len(target_lines)]

                    # 预处理窗口代码
                    clean_window = ''.join(pattern.sub('', line).lower() for line in window)

                    # 计算相似度（使用difflib）
                    ratio = SequenceMatcher(None, clean_window, clean_target).ratio()

                    if ratio >= similarity_threshold:
                        # 计算上下文代码范围
                        start_code_context = max(0, i - 10)
                        end_code_context = min(total_file_lines, i + len(target_lines) + 10)

                        # 提取上下文代码（包括前后10行）
                        context_codes = file_lines[start_code_context:end_code_context]

                        matches.append({
                            'file': str(file_path),
                            'line': start_code_context,
                            'code': context_codes,
                            'similarity': ratio
                        })

        except Exception as e:
            continue

    # 按相似度排序
    matches.sort(key=lambda x: x['similarity'], reverse=True)

    # 输出结果
    elapsed = time.time() - start_time
    logger.info(f"\n搜索完成! 共处理 {processed_files} 个文件，耗时 {elapsed:.2f} 秒")
    logger.info(f"发现 {len(matches)} 个匹配 (显示前 {top_n} 个):")

    similar_codes = []
    file_path = ''
    # 输出前N个最佳匹配
    for i, match in enumerate(matches[:top_n]):
        logger.info(f"\n匹配 #{i + 1} | 相似度: {match['similarity']:.2%}")
        logger.info(f"文件: {match['file']}:{match['line']}")
        logger.info("-" * 50)
        file_path = match['file']
        for j, line in enumerate(match['code']):
            logger.info(f"{match['line'] + j:5d} | {line}")
            similar_codes.append(line)
        logger.info("-" * 50)
    return {
        'codes': similar_codes,
        'file_path': file_path
    }