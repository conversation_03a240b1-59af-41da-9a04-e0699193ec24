import yaml
import os
import re
from typing import List, Optional
from pathlib import Path


class ReviewerConfig:
    """审核人员配置管理类"""
    
    def __init__(self, config_file: str = "conf/reviewer_config.yml"):
        self.config_file = config_file
        self.config = self._load_config()
    
    def _load_config(self) -> dict:
        """加载审核人员配置文件"""
        try:
            if not os.path.exists(self.config_file):
                # 如果配置文件不存在，创建默认配置
                self._create_default_config()
            
            with open(self.config_file, "r", encoding="utf-8") as file:
                config = yaml.safe_load(file) or {}
                return config.get('reviewer_config', {})
        except Exception as e:
            print(f"加载审核人员配置失败: {e}")
            return {}
    
    def _create_default_config(self):
        """创建默认配置文件"""
        default_config = {
            'reviewer_config': {
                'default_reviewers': ['萧国铿'],  # 默认审核人员
            }
        }
        
        # 确保目录存在
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        
        with open(self.config_file, "w", encoding="utf-8") as file:
            yaml.dump(default_config, file, default_flow_style=False, 
                     allow_unicode=True, indent=2)
        print(f"已创建默认审核人员配置文件: {self.config_file}")
    
    def get_reviewers(self, repository_name: str, branch_name: str) -> List[str]:
        """
        根据仓库名和分支名获取审核人员列表
        
        Args:
            repository_name: 仓库名称
            branch_name: 分支名称
            
        Returns:
            审核人员姓名列表
        """
        if not repository_name or not branch_name:
            return self.config.get('default_reviewers', [])
        
        # 获取仓库配置
        repo_config = self.config.get('repositories', {}).get(repository_name)
        if not repo_config:
            # 如果没有找到仓库配置，返回默认审核人员
            return self.config.get('default_reviewers', [])
        
        # 1. 优先检查特定分支配置
        branch_config = repo_config.get('branches', {})
        
        # 直接匹配分支名
        if branch_name in branch_config:
            return branch_config[branch_name]
        
        # 模式匹配（如 feature/*）
        for pattern, reviewers in branch_config.items():
            if self._match_branch_pattern(branch_name, pattern):
                return reviewers
        
        # 2. 如果没有找到特定分支配置，使用all_branches配置
        if 'all_branches' in repo_config:
            return repo_config['all_branches']
        
        # 3. 如果没有all_branches配置，使用项目默认审核人员
        if 'default_reviewers' in repo_config:
            return repo_config['default_reviewers']
        
        # 4. 最后使用全局默认审核人员
        return self.config.get('default_reviewers', [])
    
    def _match_branch_pattern(self, branch_name: str, pattern: str) -> bool:
        """
        匹配分支模式
        
        Args:
            branch_name: 实际分支名
            pattern: 模式（支持*通配符和正则表达式）
            
        Returns:
            是否匹配
        """
        # 如果模式以 regex: 开头，使用正则表达式匹配
        if pattern.startswith('regex:'):
            regex_pattern = pattern[6:]  # 去掉 'regex:' 前缀
            try:
                return bool(re.match(regex_pattern, branch_name))
            except re.error:
                print(f"无效的正则表达式: {regex_pattern}")
                return False
        
        # 原有的通配符匹配逻辑
        if '*' not in pattern:
            return branch_name == pattern
        
        # 简单的通配符匹配
        if pattern.endswith('/*'):
            prefix = pattern[:-2]
            return branch_name.startswith(prefix + '/')
        elif pattern.startswith('*/'):
            suffix = pattern[2:]
            return branch_name.endswith('/' + suffix)
        
        return False
    
    def add_repository_config(self, repository_name: str, all_branches_reviewers: List[str] = None, 
                            branch_reviewers: dict = None, default_reviewers: List[str] = None):
        """
        添加仓库配置
        
        Args:
            repository_name: 仓库名称
            all_branches_reviewers: 所有分支的审核人员
            branch_reviewers: 特定分支的审核人员配置
            default_reviewers: 项目默认审核人员
        """
        if 'repositories' not in self.config:
            self.config['repositories'] = {}
        
        repo_config = {}
        if all_branches_reviewers:
            repo_config['all_branches'] = all_branches_reviewers
        
        if branch_reviewers:
            repo_config['branches'] = branch_reviewers
        
        if default_reviewers:
            repo_config['default_reviewers'] = default_reviewers
        
        self.config['repositories'][repository_name] = repo_config
        self._save_config()
    
    def update_branch_reviewers(self, repository_name: str, branch_name: str, reviewers: List[str]):
        """
        更新特定分支的审核人员
        
        Args:
            repository_name: 仓库名称
            branch_name: 分支名称
            reviewers: 审核人员列表
        """
        if 'repositories' not in self.config:
            self.config['repositories'] = {}
        
        if repository_name not in self.config['repositories']:
            self.config['repositories'][repository_name] = {}
        
        if 'branches' not in self.config['repositories'][repository_name]:
            self.config['repositories'][repository_name]['branches'] = {}
        
        self.config['repositories'][repository_name]['branches'][branch_name] = reviewers
        self._save_config()
    
    def _save_config(self):
        """保存配置到文件"""
        try:
            full_config = {'reviewer_config': self.config}
            with open(self.config_file, "w", encoding="utf-8") as file:
                yaml.dump(full_config, file, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            print(f"审核人员配置已保存到: {self.config_file}")
        except Exception as e:
            print(f"保存审核人员配置失败: {e}")
    
    def get_all_reviewers_for_repository(self, repository_name: str) -> dict:
        """
        获取仓库的所有审核人员配置
        
        Args:
            repository_name: 仓库名称
            
        Returns:
            包含所有分支配置的字典
        """
        return self.config.get('repositories', {}).get(repository_name, {})
    
    def list_all_repositories(self) -> List[str]:
        """获取所有配置的仓库名称"""
        return list(self.config.get('repositories', {}).keys())
    
    def get_groups(self) -> dict:
        """获取所有组配置"""
        return self.config.get('groups', {})
    
    def get_group_by_name(self, group_name: str) -> Optional[dict]:
        """根据组名获取组配置"""
        groups = self.get_groups()
        return groups.get(group_name)
    
    def get_repositories_by_group(self, group_name: str) -> List[str]:
        """根据组名获取该组下的所有仓库"""
        group = self.get_group_by_name(group_name)
        if group:
            return group.get('repositories', [])
        return []
    
    def get_group_by_repository(self, repository_name: str) -> Optional[str]:
        """根据仓库名获取所属的组名"""
        groups = self.get_groups()
        for group_name, group_config in groups.items():
            repositories = group_config.get('repositories', [])
            if repository_name in repositories:
                return group_name
        return None
    
    def list_all_groups(self) -> List[str]:
        """获取所有组名"""
        return list(self.config.get('groups', {}).keys())
    
    def get_group_webhook_url(self, group_name: str) -> Optional[str]:
        """根据组名获取webhook_url"""
        group = self.get_group_by_name(group_name)
        if group:
            return group.get('webhook_url')
        return None
    
    def get_webhook_url_by_repository(self, repository_name: str) -> Optional[str]:
        """根据仓库名获取对应的webhook_url"""
        group_name = self.get_group_by_repository(repository_name)
        if group_name:
            return self.get_group_webhook_url(group_name)
        return None


# 全局实例
# 根目录
project_root = Path(__file__).parent.parent
reviewer_config = ReviewerConfig(f'{project_root}/conf/reviewer_config.yml')


def get_reviewers_for_commit(repository_name: str, branch_name: str) -> List[str]:
    """
    便捷函数：获取提交的审核人员
    
    Args:
        repository_name: 仓库名称
        branch_name: 分支名称
        
    Returns:
        审核人员姓名列表
    """
    return reviewer_config.get_reviewers(repository_name, branch_name)


if __name__ == '__main__':
    # 测试获取审核人员
    print("测试获取审核人员:")
    print(f"37sdk_AS/dev-3.7.9.4: {reviewer_config.get_reviewers('37sdk_AS', 'dev-3.7.9.4')}")
    print(f"37sdk_AS/multi-dev-3.6.6: {reviewer_config.get_reviewers('37sdk_AS', 'multi-dev-3.6.6')}")
    print(f"37MultiSDKConvertTool_Factory/develop: {reviewer_config.get_reviewers('37MultiSDKConvertTool_Factory', 'develop')}")
    print(f"37sy_sdk_multiChannel/feature/login: {reviewer_config.get_reviewers('37sy_sdk_multiChannel', 'feature/login')}")
    print(f"HarmonySqSdk/any_branch: {reviewer_config.get_reviewers('HarmonySqSdk', 'any_branch')}")
    
    # 测试添加配置
    # print("\n测试添加配置:")
    # config.add_repository_config(
    #     'new_project',
    #     all_branches_reviewers=['xx0'],
    #     branch_reviewers={
    #         'master': ['xx1'],
    #         'hotfix/*': ['xx2']
    #     }
    # )
