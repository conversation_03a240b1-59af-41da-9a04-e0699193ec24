"""
用户映射管理模块
用于处理用户名到UID的映射关系
"""
import os
import yaml
from typing import Dict, Optional, List
from pathlib import Path


class UserMapping:
    """用户映射管理类"""
    
    def __init__(self, config_file: str = "conf/user_map_uid_config.yml"):
        self.config_file = config_file
        self.user_map = self._load_config()
        self.reverse_map = self._build_reverse_map()
    
    def _load_config(self) -> Dict[str, str]:
        """加载用户映射配置"""
        try:
            if not os.path.exists(self.config_file):
                print(f"用户映射配置文件不存在: {self.config_file}")
                self._create_default_config()
            
            with open(self.config_file, "r", encoding="utf-8") as file:
                config = yaml.safe_load(file) or {}
                return config.get('user_map_uid_config', {})
        except Exception as e:
            print(f"加载用户映射配置失败: {e}")
            return {}
    
    def _create_default_config(self):
        """创建默认配置文件"""
        default_config = {
            'user_map_uid_config': {
                '邹龙生': '704139367',
                '萧国铿': '181137171',
                '关司平': '178846355',
                '戚智超': '154536188',
                '彭家豪': '157162197',
                '何天从': '152154755',
                '李莉莉2号': '191376857',
                '何聚敛': '705729010',
                '张国祥': 'zhangguoxiang',
            }
        }
        
        # 确保目录存在
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        
        with open(self.config_file, "w", encoding="utf-8") as file:
            yaml.dump(default_config, file, default_flow_style=False, 
                     allow_unicode=True, indent=2)
        print(f"已创建默认用户映射配置文件: {self.config_file}")
    
    def _build_reverse_map(self) -> Dict[str, str]:
        """构建反向映射（UID到用户名）"""
        return {uid: username for username, uid in self.user_map.items()}
    
    def get_uid_by_username(self, username: str) -> Optional[str]:
        """根据用户名获取UID"""
        return self.user_map.get(username)
    
    def get_username_by_uid(self, uid: str) -> Optional[str]:
        """根据UID获取用户名"""
        return self.reverse_map.get(uid)

    
    def add_user_mapping(self, username: str, uid: str) -> bool:
        """添加用户映射"""
        try:
            self.user_map[username] = uid
            self.reverse_map[uid] = username
            self._save_config()
            return True
        except Exception as e:
            print(f"添加用户映射失败: {e}")
            return False
    
    def remove_user_mapping(self, username: str) -> bool:
        """删除用户映射"""
        try:
            if username in self.user_map:
                uid = self.user_map[username]
                del self.user_map[username]
                if uid in self.reverse_map:
                    del self.reverse_map[uid]
                self._save_config()
                return True
            return False
        except Exception as e:
            print(f"删除用户映射失败: {e}")
            return False
    
    def update_user_mapping(self, username: str, new_uid: str) -> bool:
        """更新用户映射"""
        try:
            if username in self.user_map:
                old_uid = self.user_map[username]
                # 删除旧的反向映射
                if old_uid in self.reverse_map:
                    del self.reverse_map[old_uid]
                
                # 更新映射
                self.user_map[username] = new_uid
                self.reverse_map[new_uid] = username
                self._save_config()
                return True
            return False
        except Exception as e:
            print(f"更新用户映射失败: {e}")
            return False
    
    def _save_config(self):
        """保存配置到文件"""
        try:
            full_config = {'user_map_uid_config': self.user_map}
            with open(self.config_file, "w", encoding="utf-8") as file:
                yaml.dump(full_config, file, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            print(f"用户映射配置已保存到: {self.config_file}")
        except Exception as e:
            print(f"保存用户映射配置失败: {e}")
    
    def get_all_mappings(self) -> Dict[str, str]:
        """获取所有用户映射"""
        return self.user_map.copy()
    
    def get_all_usernames(self) -> List[str]:
        """获取所有用户名"""
        return list(self.user_map.keys())
    
    def get_all_uids(self) -> List[str]:
        """获取所有UID"""
        return list(self.user_map.values())
    
    def search_users(self, keyword: str) -> Dict[str, str]:
        """搜索用户（支持用户名和UID搜索）"""
        results = {}
        keyword_lower = keyword.lower()
        
        for username, uid in self.user_map.items():
            if (keyword_lower in username.lower() or 
                keyword_lower in uid.lower()):
                results[username] = uid
        
        return results
    
    def validate_mapping(self) -> List[str]:
        """验证映射配置的有效性"""
        issues = []
        
        # 检查重复的UID
        uid_counts = {}
        for username, uid in self.user_map.items():
            if uid in uid_counts:
                uid_counts[uid].append(username)
            else:
                uid_counts[uid] = [username]
        
        for uid, usernames in uid_counts.items():
            if len(usernames) > 1:
                issues.append(f"UID '{uid}' 被多个用户使用: {', '.join(usernames)}")
        
        # 检查空值
        for username, uid in self.user_map.items():
            if not username.strip():
                issues.append("发现空的用户名")
            if not uid.strip():
                issues.append(f"用户 '{username}' 的UID为空")
        
        return issues
    
    def get_stats(self) -> Dict[str, int]:
        """获取映射统计信息"""
        return {
            'total_mappings': len(self.user_map),
            'unique_usernames': len(set(self.user_map.keys())),
            'unique_uids': len(set(self.user_map.values()))
        }


# 全局用户映射实例
user_mapping = UserMapping()


def get_uid_by_username(username: str) -> Optional[str]:
    """便捷函数：根据用户名获取UID"""
    return user_mapping.get_uid_by_username(username)


def get_username_by_uid(uid: str) -> Optional[str]:
    """便捷函数：根据UID获取用户名"""
    return user_mapping.get_username_by_uid(uid)
