import os
from lib.llm.code_reviewer import <PERSON>Reviewer
from lib.token_util import count_tokens, truncate_text_by_tokens


class HistoryCodeReviewer(BaseReviewer):

    def __init__(self, logger):
        super().__init__("code_review_prompt", "conf/history_prompt_templates.yml", logger)

    def review_and_strip_code(self, commit_code: str, history_code: str, commit_text: str) -> str:
        """
                Review判断changes_text超出取前REVIEW_MAX_TOKENS个token，超出则截断changes_text，
                调用review_code方法，返回review_result，如果review_result是markdown格式，则去掉头尾的```
                :param commit_text:
                :param commit_code:
                :param history_code:
                :return:
                """
        # 如果超长，取前REVIEW_MAX_TOKENS个token
        review_max_tokens = int(os.getenv("REVIEW_MAX_TOKENS", 10000))
        # 如果changes为空,打印日志
        if not commit_code:
            self.logger.info("代码为空, diffs_text = %", str(commit_code))
            return "代码为空"

        # 计算tokens数量，如果超过REVIEW_MAX_TOKENS，截断changes_text
        tokens_count = count_tokens(commit_code)
        if tokens_count > review_max_tokens:
            commit_code = truncate_text_by_tokens(commit_code, review_max_tokens)
        tokens_count = count_tokens(history_code)
        if tokens_count > review_max_tokens:
            history_code = truncate_text_by_tokens(history_code, review_max_tokens)

        review_result = self.review_code(commit_code, history_code, commit_text)
        review_result = review_result.strip()
        if review_result.startswith("```markdown") and review_result.endswith("```"):
            return review_result[11:-3].strip()
        return review_result

    def review_code(self, commit_code: str, history_code: str, commits_text: str) -> str:
        """Review 代码并返回结果"""
        messages = [
            self.prompts["system_message"],
            {
                "role": "user",
                "content": self.prompts["user_message"]["content"].format(
                    commit_code=commit_code, history_code=history_code, commits_text=commits_text
                ),
            },
        ]
        return self.call_llm(messages)
