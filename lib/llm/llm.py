import aiohttp
import asyncio
from typing import Dict, Any
import sys
import platform


async def bot_chat(text: str, from_lang: str, to_lang: str, options: Dict[str, Any]) -> str:
    print(f'提问内容：{text}')
    config = options.get("config", {})
    set_result = options.get("setResult")
    bot_id = options.get("botId")
    token = options.get("token")
    # detect 参数未在函数中使用，故忽略

    # 覆盖 requestPath
    request_path = "https://ai-service.39on.com/api/ai-service/v1/ai_bot/bot_chat_verify"
    headers = {
        'Content-Type': 'application/json',
        'X-Client-Type': 'PC',
        'X-Client-OS': options.get("platform", "Unknown"),  # 从 options 获取平台信息
        'Token': token
    }

    data = {
        "bot_marking": bot_id,
        "message": {
            "role": "",
            "content": [
                {
                    "type": "text",
                    "text": text
                }
            ]
        }
    }

    final_result = ""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                    request_path,
                    headers=headers,
                    json=data
            ) as response:
                if response.status != 200:
                    raise Exception(f"请求失败，状态码: {response.status}")

                # 流式读取响应内容
                async for chunk in response.content.iter_any():
                    decoded_chunk = chunk.decode('utf-8')
                    final_result += decoded_chunk
                    if set_result:
                        set_result(final_result)  # 调用回调函数更新结果

    except Exception as err:
        raise Exception(f"请求失败，错误信息: {err}")

    return final_result


'''
bot_id
■ 豆包
●  4vbhmjmD7zPxomg2
■ kimi 
● UEuQRBYC7XfLiGSr
■ 通义千问 
● VPukVR7rvwroblof
■ DS-R1 
● dLQ9xKBzuShs0nvd
■ 智谱  
● ODj9XmuZgxSGseir
'''


def run_bot_chat(text):
    options = {
        "config": {},  
        "setResult": lambda res: res,
        "botId": "dLQ9xKBzuShs0nvd",
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lIjoi6YK56b6Z55SfIiwiZXhwIjo2Nzk3NzQ0ODA1LCJpYXQiOjE3NDg5NDU0MzcsImlzcyI6IkxKQUlTZXJ2aWNlIn0.RD6gbRVq_Tb-_Vh1GFaHBToPf9am5CeFpO7z_1h7mxc",
        "platform": get_os_type(),
    }
    result = asyncio.run(bot_chat(text, "zh", "en", options))
    print("最终结果:", result)
    return result


def get_os_type():
    # 获取 sys.platform 并转换为小写
    system = sys.platform.lower()

    # 匹配不同系统类型
    if system.startswith('darwin'):
        return 'MacOS'
    elif system.startswith('win'):
        return 'Windows'
    elif system.startswith('linux'):
        return 'Linux'
    else:
        # 尝试使用 platform 模块作为备选方案
        try:
            system = platform.system().lower()
            if system == 'darwin':
                return 'MacOS'
            elif system == 'windows':
                return 'Windows'
            elif system == 'linux':
                return 'Linux'
        except ImportError:
            pass
        return 'UNKNOWN'


if __name__ == '__main__':
    content = '''
    以下是某位员工向 GitLab 代码库提交的代码，请以审查以下代码。

代码变更内容：
[{'diff': '@@ -15,6 +15,11 @@ def function3(test):\n     print(test)\n \n def function4(test):\n+    print(test)\n+    print(test)\n+    print(test)\n+\n+def function5(test):\n     print(test)\n     print(test)\n     print(test)\n\\ No newline at end of file\n', 'new_path': 'test.py'}]

提交历史(commits)：
m-test代码改动5
2025-06-03 19:14:13,665 - INFO - code_reviewer.py:call_llm:46 - 向 AI 发送代码 Review 请求, messages: 以下是某位员工向 GitLab 代码库提交的代码，请审查以下代码。

代码变更内容：
[{'diff': '@@ -15,6 +15,11 @@ def function3(test):\n     print(test)\n \n def function4(test):\n+    print(test)\n+    print(test)\n+    print(test)\n+\n+def function5(test):\n     print(test)\n     print(test)\n     print(test)\n\\ No newline at end of file\n', 'new_path': 'test.py'}]

提交历史(commits)：
m-test代码改动5
    '''
    run_bot_chat(content)
