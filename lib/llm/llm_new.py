import time
import requests

from log import get_logger

ai_code_review_url = 'https://qx-da.39on.com/api/code-review'


'''
"deepseek-r1"
"clould3.7"
"gpt4.1"
"gpt-4o"
'''

def request_code_review(content, logger):
    headers = {
        'Content-Type': 'application/json'
    }
    data = {
        'diffs_text': content,
        'model': 'gpt4.1'
    }
    start_time = time.time()
    response = requests.post(ai_code_review_url, headers=headers, json=data, verify=False, timeout=600)
    end_time = time.time()
    duration = end_time - start_time
    logger.info(f"llm执行耗时: {duration:.6f} 秒")
    if response.status_code == 200:
        data = response.json()
        status = data['status']
        if status == 'success':
            return data['suggest']
        else:
            logger.info(f'代码审核接口异常, status = {status}')
            return ""
    else:
        logger.info(f'代码审核接口异常, response.status_code = {response.status_code}')
        return ""


if __name__ == '__main__':
    content = '''
    以下是某位员工向 GitLab 代码库提交的代码，请以审查以下代码。

代码变更内容：
[{'diff': '@@ -15,6 +15,11 @@ def function3(test):\n     print(test)\n \n def function4(test):\n+    print(test)\n+    print(test)\n+    print(test)\n+\n+def function5(test):\n     print(test)\n     print(test)\n     print(test)\n\\ No newline at end of file\n', 'new_path': 'test.py'}]

提交历史(commits)：
m-test代码改动5
2025-06-03 19:14:13,665 - INFO - code_reviewer.py:call_llm:46 - 向 AI 发送代码 Review 请求, messages: 以下是某位员工向 GitLab 代码库提交的代码，请审查以下代码。

代码变更内容：
[{'diff': '@@ -15,6 +15,11 @@ def function3(test):\n     print(test)\n \n def function4(test):\n+    print(test)\n+    print(test)\n+    print(test)\n+\n+def function5(test):\n     print(test)\n     print(test)\n     print(test)\n\\ No newline at end of file\n', 'new_path': 'test.py'}]

提交历史(commits)：
m-test代码改动5
    '''
    print(request_code_review(content, get_logger('llm_new_test')))
