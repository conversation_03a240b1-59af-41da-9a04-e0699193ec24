from pathlib import Path
import schedule
import time
import threading
from lib.model.ApiUsage import ReviewRecord
from lib.notification.wechat import send_group_pending_notification
from lib.reviewer_config import reviewer_config
from codereview_app import api_app
from log import get_logger

def check_pending_reviews():
    """检查待审核的记录并按组发送提醒通知"""
    logger = get_logger('scheduler')
    
    with api_app.app_context():
        try:
            # 获取所有组配置
            groups = reviewer_config.get_groups()
            if not groups:
                logger.info("没有配置任何组，跳过检查")
                return
            
            # 按组检查待审核记录
            for group_key, group_info in groups.items():
                try:
                    # 获取该组下的所有仓库
                    repositories = group_info.get('repositories', [])
                    if not repositories:
                        logger.info(f"组 '{group_key}' 没有配置仓库，跳过")
                        continue
                    
                    # 查询该组下待审核的记录
                    from sqlalchemy import or_
                    group_pending_records = ReviewRecord.query.filter(
                        ReviewRecord.useful.is_(None),
                        or_(*[ReviewRecord.project_name == repo for repo in repositories])
                    ).all()
                    
                    if not group_pending_records:
                        logger.info(f"组 '{group_key}' 没有待审核的记录")
                        continue
                    
                    logger.info(f"组 '{group_key}' 发现 {len(group_pending_records)} 条待审核记录")
                    
                    # 为组发送一条汇总通知
                    send_group_pending_notification(group_key, group_info, group_pending_records, logger)
                    
                except Exception as e:
                    logger.error(f"检查组 '{group_key}' 待审核记录失败: {str(e)}")
                    
        except Exception as e:
            logger.error(f"检查待审核记录失败: {str(e)}")

def start_scheduler():
    """启动定时任务调度器"""
    logger = get_logger('scheduler')

    def scheduled_check_with_cleanup():
        """执行检查并清理日志"""
        try:
            check_pending_reviews()
        finally:
            # 清理scheduler日志
            from log import remove_logger
            project_root_path = Path(__file__).resolve().parent
            remove_logger('scheduler', f"{project_root_path}/log/scheduler.log")
    
    # 每天上午11点执行检查
    schedule.every().day.at("11:00").do(scheduled_check_with_cleanup)
    
    # 也可以设置为每24小时执行一次
    # schedule.every(24).hours.do(scheduled_check_with_cleanup)
    
    logger.info("定时任务调度器已启动，每天11点检查待审核记录")

    def run_scheduler():
        while True:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次是否有任务需要执行
            except Exception as e:
                logger.error(f"定时任务执行异常: {str(e)}")
                time.sleep(60)
    
    # 在后台线程中运行调度器
    scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
    scheduler_thread.start()
    
    return scheduler_thread

def check_pending_reviews_manual():
    """手动触发检查待审核记录（用于测试）"""
    logger = get_logger('manual_check')
    logger.info("手动触发检查待审核记录")
    check_pending_reviews()

if __name__ == '__main__':
    # 测试用
    check_pending_reviews_manual()
