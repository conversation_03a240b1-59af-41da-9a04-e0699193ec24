import logging
import os
from logging.handlers import RotatingFileHandler
from pathlib import Path
from typing import Dict


# 自定义 Logger 类，重写 warn 和 error 方法
class CustomLogger(logging.Logger):
    def warn(self, msg, *args, **kwargs):
        # 在 warn 消息前添加 ⚠️
        msg_with_emoji = f"⚠️ {msg}"
        super().warning(msg_with_emoji, *args, **kwargs)  # 注意：warn 是 warning 的别名

    def error(self, msg, *args, **kwargs):
        # 在 error 消息前添加 ❌
        msg_with_emoji = f"❌ {msg}"
        super().error(msg_with_emoji, *args, **kwargs)


log_max_bytes = int(os.environ.get("LOG_MAX_BYTES", 10 * 1024 * 1024))  # 默认10MB
log_backup_count = int(os.environ.get("LOG_BACKUP_COUNT", 5))  # 默认保留5个备份文件
# 设置日志级别
log_level = os.environ.get("LOG_LEVEL", "INFO")
LOG_LEVEL = getattr(logging, log_level.upper(), logging.INFO)

console_handler = logging.StreamHandler()
console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(filename)s:%(funcName)s:%(lineno)d - %(message)s'))
console_handler.setLevel(LOG_LEVEL)

logger_map: Dict[str, CustomLogger] = {}
    
def get_logger(log_file_name):

    logger = logger_map.get(log_file_name)
    if not logger:
        # 检查目录是否存在，不存在则创建
        project_root_path = Path(__file__).resolve().parent
        log_dir = f'{project_root_path}/log'
        if not os.path.exists(log_dir):
            try:
                os.makedirs(log_dir)  # 创建目录，包括任何必要的中间目录
                print(f"目录 {log_dir} 已创建。")
            except Exception as e:
                # 如果创建目录失败，记录错误并使用备用方案（例如控制台输出）
                print(f"创建目录 {log_dir} 失败: {e}")
        log_file = f'{project_root_path}/log/{log_file_name}.log'
        file_handler = RotatingFileHandler(
            filename=log_file,
            mode='a',
            maxBytes=log_max_bytes,
            backupCount=log_backup_count,
            encoding='utf-8',
            delay=False
        )
        file_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(levelname)s - %(filename)s:%(funcName)s:%(lineno)d - %(message)s'))
        file_handler.setLevel(LOG_LEVEL)
        # 使用自定义的 Logger 类
        logger = CustomLogger(__name__)
        logger.setLevel(LOG_LEVEL)  # 设置 Logger 的日志级别
        logger.addHandler(console_handler)
        logger.addHandler(file_handler)
        logger_map[log_file_name] = logger
    return logger

def remove_logger(log_file_name, log_file_path):
    try:
        if logger_map[log_file_name]:
            del logger_map[log_file_name]
            safe_delete_file(log_file_path)
            print(f'清除logger成功, key = {log_file_name}')
    except Exception as e:
        print(f'清除logger异常, error = {e}')


def safe_delete_file(file_path):
    try:
        if os.path.isfile(file_path):  # 确保是文件，不是目录
            os.remove(file_path)
            print(f"成功删除: {file_path}")
        else:
            print(f"路径不是文件: {file_path}")
    except PermissionError:
        print(f"权限不足，无法删除: {file_path}")
    except Exception as e:
        print(f"删除失败: {str(e)}")