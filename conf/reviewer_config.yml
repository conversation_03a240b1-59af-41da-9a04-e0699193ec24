reviewer_config:
  # 全局默认审核人员（当没有找到任何配置时使用）
  default_reviewers:
    - 萧国铿
  
  # 组配置
  groups:
    # 客户端组
    sy_client_group:
      name: "37手游-客户端组"
      description: "37手游-客户端组项目"
      webhook_url: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c4754c3a-9ccd-4e9e-89fd-b945b76f54bf"
      repositories:
        - 37sdk_AS
        - 37MultiSDKConvertTool_Factory
        - 37sy_sdk_multiChannel
        - HarmonySqSdk
        - dlyz_flutter
        - 37MobileSea
        - SharkSDK
        - red_crystal_sdk
        - jailbreak
  
  # 仓库配置
  repositories:
    # 自营工程
    37sdk_AS:
      # 项目默认审核人员（当分支没有匹配到时使用）
      default_reviewers:
        - 关司平
      branches:
        # 正则匹配：dev-开头的分支
        "regex:^dev-.*":
          - 关司平
        # 正则匹配：multi-dev-开头的分支
        "regex:^multi-dev-.*":
          - 戚智超
    
    # 打包工程
    37MultiSDKConvertTool_Factory:
      default_reviewers:
        - 关司平
    
    # 渠道工程
    37sy_sdk_multiChannel:
      default_reviewers:
        - 关司平
        - 戚智超
    
    # 鸿蒙工程
    HarmonySqSdk:
      default_reviewers:
        - 萧国铿
        - 邹龙生

    # 斗罗宇宙Flutter工程
    dlyz_flutter:
      default_reviewers:
        - 萧国铿

    # 海外Android工程
    37MobileSea:
      default_reviewers:
        - 彭家豪

    # 海外IOS工程
    SharkSDK:
      default_reviewers:
        - 彭家豪

    # 国内IOS工程
    red_crystal_sdk:
      default_reviewers:
        - 何天从
        - 李莉莉2号

    # 国内IOS工程
    jailbreak:
      default_reviewers:
        - 何天从
        - 李莉莉2号